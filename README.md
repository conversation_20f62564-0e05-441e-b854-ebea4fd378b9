# 激光雷达网络参数自动检测工具

这个工具可以自动监听以太网接口，统计最频繁出现的目标IP和端口号，用于快速识别激光雷达的网络配置参数，无需手动使用Wireshark。

## 功能特点

- 🚀 **自动检测**: 无需手动配置，自动识别激光雷达网络参数
- 📊 **统计分析**: 统计数据包频率，识别最可能的目标IP和端口
- ⏱️ **实时监控**: 显示监听进度和实时统计信息
- 🎯 **智能推测**: 自动推测设备端口号（通常为数据端口+1）
- 💻 **命令行界面**: 简单易用的命令行工具

## 安装依赖

```bash
pip install scapy
```

## 使用方法

### 基本使用

```bash
# 使用默认设置（监听30秒）
python lidar_network_detector.py

# 指定监听时间（60秒）
python lidar_network_detector.py -t 60

# 指定网络接口
python lidar_network_detector.py -i "以太网"

# 设置最小数据包阈值
python lidar_network_detector.py -m 10
```

### 参数说明

- `-i, --interface`: 指定网络接口名称（默认监听所有接口）
- `-t, --time`: 监听时长（秒），默认30秒
- `-m, --min-packets`: 最小数据包数量阈值，默认5个

### 使用步骤

1. **连接激光雷达**: 使用以太网线将激光雷达连接到您的电脑
2. **启动激光雷达**: 确保激光雷达正常工作并开始发送数据
3. **运行检测工具**: 
   ```bash
   python lidar_network_detector.py
   ```
4. **等待检测完成**: 工具会自动监听网络数据包并分析
5. **查看结果**: 工具会显示检测到的目标IP、数据端口和推测的设备端口

## 输出示例

```
============================================================
激光雷达网络参数自动检测工具
============================================================
可用网络接口: 以太网, WLAN
将监听所有接口...
监听时长: 30 秒
最小数据包阈值: 5
------------------------------------------------------------
开始监听网络数据包...
请确保激光雷达正在工作并发送数据...
------------------------------------------------------------
监听进度: 30s/30s, 已捕获UDP数据包: 1247, 剩余时间: 0s
============================================================
监听完成，正在分析结果...

检测到 3 个不同的目标地址
------------------------------------------------------------
排名 目标IP          端口     数据包数   占比    
------------------------------------------------------------
1    *************   2368     1156       92.7%
2    *************   8080     78         6.3%
3    *********       5353     13         1.0%
------------------------------------------------------------

🎯 检测到的激光雷达参数:
   目标IP: *************
   数据端口: 2368
   设备端口: 2369 (通常为数据端口+1)
   数据包数量: 1156
   占比: 92.7%

✅ 检测成功！建议的激光雷达配置:
   目标IP: *************
   数据端口: 2368
   设备端口: 2369
============================================================
```

## 工作原理

1. **数据包捕获**: 使用scapy库监听指定网络接口的UDP数据包
2. **数据过滤**: 过滤掉系统端口（<1024），专注于激光雷达可能使用的端口
3. **统计分析**: 统计每个目标IP和端口组合的出现频率
4. **结果识别**: 识别出现频率最高的组合作为激光雷达参数

## 注意事项

- **管理员权限**: 在某些系统上可能需要管理员权限来捕获网络数据包
- **防火墙设置**: 确保防火墙不会阻止网络数据包捕获
- **激光雷达状态**: 确保激光雷达正在工作并发送数据
- **网络连接**: 确保激光雷达与电脑的网络连接正常

## 故障排除

### 未捕获到数据包
- 检查激光雷达是否正常工作
- 检查网络连接是否正常
- 尝试使用管理员权限运行程序
- 检查防火墙设置

### 检测结果不准确
- 增加监听时间（使用 `-t` 参数）
- 调整最小数据包阈值（使用 `-m` 参数）
- 确保监听期间只有目标激光雷达在发送数据

## 支持的激光雷达类型

这个工具适用于大多数通过以太网发送UDP数据包的激光雷达，包括但不限于：
- Velodyne系列
- Ouster系列
- Livox系列
- Hesai系列
- RoboSense系列

## 许可证

MIT License
