#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光雷达快速检测脚本
简化版本，快速检测激光雷达网络参数
"""

import time
from collections import defaultdict
from scapy.all import sniff, UDP, IP
import sys


def quick_detect_lidar(duration=15):
    """
    快速检测激光雷达网络参数
    
    Args:
        duration: 监听时长（秒），默认15秒
    """
    print("🔍 激光雷达快速检测工具")
    print(f"⏱️  监听时长: {duration} 秒")
    print("📡 开始监听网络数据包...")
    print("请确保激光雷达正在工作...")
    print("-" * 40)
    
    packet_stats = defaultdict(int)
    start_time = time.time()
    
    def packet_handler(packet):
        try:
            if packet.haslayer(UDP) and packet.haslayer(IP):
                dst_ip = packet[IP].dst
                dst_port = packet[UDP].dport
                
                # 过滤激光雷达常用端口范围
                if 2000 <= dst_port <= 9000:
                    key = (dst_ip, dst_port)
                    packet_stats[key] += 1
                    
                    # 简单进度显示
                    elapsed = time.time() - start_time
                    if int(elapsed) % 3 == 0:
                        total = sum(packet_stats.values())
                        print(f"\r⏳ 已监听 {elapsed:.0f}s, 捕获数据包: {total}", end="", flush=True)
        except:
            pass
    
    try:
        # 开始监听
        sniff(
            prn=packet_handler,
            filter="udp",
            timeout=duration,
            store=False
        )
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断")
        return None
    except Exception as e:
        print(f"\n\n❌ 错误: {e}")
        return None
    
    print(f"\n\n📊 分析结果:")
    print("-" * 40)
    
    if not packet_stats:
        print("❌ 未检测到激光雷达数据")
        print("请检查:")
        print("  • 激光雷达是否正常工作")
        print("  • 网络连接是否正常")
        print("  • 是否需要管理员权限")
        return None
    
    # 找出最频繁的目标
    sorted_stats = sorted(packet_stats.items(), key=lambda x: x[1], reverse=True)
    top_target = sorted_stats[0]
    (dst_ip, dst_port), count = top_target
    
    total_packets = sum(packet_stats.values())
    percentage = (count / total_packets) * 100
    
    print(f"🎯 检测结果:")
    print(f"   目标IP: {dst_ip}")
    print(f"   数据端口: {dst_port}")
    print(f"   设备端口: {dst_port + 1} (推测)")
    print(f"   数据包数: {count}")
    print(f"   占比: {percentage:.1f}%")
    
    if percentage > 70:
        print("✅ 检测置信度: 高")
    elif percentage > 40:
        print("⚠️  检测置信度: 中等")
    else:
        print("❌ 检测置信度: 低")
    
    return {
        'ip': dst_ip,
        'data_port': dst_port,
        'device_port': dst_port + 1,
        'confidence': percentage
    }


if __name__ == "__main__":
    # 检查命令行参数
    duration = 15
    if len(sys.argv) > 1:
        try:
            duration = int(sys.argv[1])
        except ValueError:
            print("❌ 无效的时间参数，使用默认值15秒")
    
    print("=" * 50)
    result = quick_detect_lidar(duration)
    print("=" * 50)
    
    if result:
        print(f"\n💡 建议配置:")
        print(f"   目标IP: {result['ip']}")
        print(f"   数据端口: {result['data_port']}")
        print(f"   设备端口: {result['device_port']}")
    else:
        print(f"\n🔄 建议:")
        print(f"   • 延长监听时间: python quick_detect.py 30")
        print(f"   • 使用完整版工具: python lidar_network_detector.py")
