#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光雷达网络参数自动检测工具
自动监听以太网接口，统计最频繁出现的目标IP和端口号
用于快速识别激光雷达的网络配置参数
"""

import time
import threading
from collections import defaultdict, Counter
from scapy.all import sniff, UDP, IP
import argparse
import sys
import signal


class LidarNetworkDetector:
    def __init__(self, interface=None, duration=30, min_packets=5):
        """
        初始化激光雷达网络检测器
        
        Args:
            interface: 网络接口名称，None表示自动选择
            duration: 监听持续时间（秒）
            min_packets: 最小数据包数量阈值
        """
        self.interface = interface
        self.duration = duration
        self.min_packets = min_packets
        self.packet_stats = defaultdict(int)  # 存储 (dst_ip, dst_port) -> count
        self.running = False
        self.start_time = None
        
    def packet_handler(self, packet):
        """
        数据包处理函数
        """
        try:
            # 只处理UDP数据包
            if packet.haslayer(UDP) and packet.haslayer(IP):
                dst_ip = packet[IP].dst
                dst_port = packet[UDP].dport
                
                # 过滤掉一些常见的系统端口，专注于激光雷达可能使用的端口
                if dst_port > 1024:  # 通常激光雷达使用的端口都大于1024
                    key = (dst_ip, dst_port)
                    self.packet_stats[key] += 1
                    
                    # 实时显示进度
                    elapsed = time.time() - self.start_time
                    if int(elapsed) % 5 == 0 and elapsed > 0:  # 每5秒显示一次
                        self.print_progress(elapsed)
                        
        except Exception as e:
            # 忽略解析错误，继续处理其他数据包
            pass
    
    def print_progress(self, elapsed):
        """
        打印监听进度
        """
        total_packets = sum(self.packet_stats.values())
        remaining = max(0, self.duration - elapsed)
        print(f"\r监听进度: {elapsed:.0f}s/{self.duration}s, "
              f"已捕获UDP数据包: {total_packets}, "
              f"剩余时间: {remaining:.0f}s", end="", flush=True)
    
    def get_network_interfaces(self):
        """
        获取可用的网络接口列表
        """
        try:
            from scapy.all import get_if_list
            interfaces = get_if_list()
            return [iface for iface in interfaces if not iface.startswith('lo')]
        except:
            return []
    
    def detect_lidar_params(self):
        """
        检测激光雷达网络参数
        """
        print("=" * 60)
        print("激光雷达网络参数自动检测工具")
        print("=" * 60)
        
        # 显示网络接口信息
        if self.interface is None:
            interfaces = self.get_network_interfaces()
            if interfaces:
                print(f"可用网络接口: {', '.join(interfaces)}")
                print("将监听所有接口...")
            else:
                print("未找到可用网络接口，将使用默认设置")
        else:
            print(f"监听网络接口: {self.interface}")
        
        print(f"监听时长: {self.duration} 秒")
        print(f"最小数据包阈值: {self.min_packets}")
        print("-" * 60)
        
        # 开始监听
        print("开始监听网络数据包...")
        print("请确保激光雷达正在工作并发送数据...")
        print("-" * 60)
        
        self.running = True
        self.start_time = time.time()
        
        try:
            # 使用scapy监听数据包
            sniff(
                iface=self.interface,
                prn=self.packet_handler,
                filter="udp",  # 只捕获UDP数据包
                timeout=self.duration,
                store=False  # 不存储数据包，节省内存
            )
        except KeyboardInterrupt:
            print("\n\n用户中断监听...")
        except Exception as e:
            print(f"\n\n监听过程中出现错误: {e}")
        finally:
            self.running = False
        
        print("\n" + "=" * 60)
        print("监听完成，正在分析结果...")
        
        return self.analyze_results()
    
    def analyze_results(self):
        """
        分析监听结果
        """
        if not self.packet_stats:
            print("未捕获到任何UDP数据包！")
            print("请检查：")
            print("1. 激光雷达是否正常工作")
            print("2. 网络连接是否正常")
            print("3. 是否需要管理员权限运行此程序")
            return None
        
        # 按数据包数量排序
        sorted_stats = sorted(self.packet_stats.items(), key=lambda x: x[1], reverse=True)
        
        print(f"\n检测到 {len(sorted_stats)} 个不同的目标地址")
        print("-" * 60)
        print(f"{'排名':<4} {'目标IP':<15} {'端口':<8} {'数据包数':<10} {'占比':<8}")
        print("-" * 60)
        
        total_packets = sum(self.packet_stats.values())
        likely_lidar = None
        
        for i, ((dst_ip, dst_port), count) in enumerate(sorted_stats[:10], 1):
            percentage = (count / total_packets) * 100
            print(f"{i:<4} {dst_ip:<15} {dst_port:<8} {count:<10} {percentage:.1f}%")
            
            # 判断最可能的激光雷达参数
            if i == 1 and count >= self.min_packets:
                likely_lidar = (dst_ip, dst_port, count, percentage)
        
        print("-" * 60)
        
        if likely_lidar:
            dst_ip, dst_port, count, percentage = likely_lidar
            print(f"\n🎯 检测到的激光雷达参数:")
            print(f"   目标IP: {dst_ip}")
            print(f"   数据端口: {dst_port}")
            print(f"   数据包数量: {count}")
            print(f"   占比: {percentage:.1f}%")
            
            # 尝试推测设备端口（通常是数据端口+1）
            device_port = dst_port + 1
            print(f"   推测设备端口: {device_port} (通常为数据端口+1)")
            
            return {
                'target_ip': dst_ip,
                'data_port': dst_port,
                'device_port': device_port,
                'packet_count': count,
                'percentage': percentage
            }
        else:
            print(f"\n⚠️  未检测到明显的激光雷达数据流")
            print(f"   (数据包数量最多的目标少于 {self.min_packets} 个)")
            return None


def signal_handler(sig, frame):
    """
    信号处理函数，用于优雅退出
    """
    print('\n\n程序被用户中断')
    sys.exit(0)


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='激光雷达网络参数自动检测工具')
    parser.add_argument('-i', '--interface', type=str, default=None,
                        help='指定网络接口名称（默认监听所有接口）')
    parser.add_argument('-t', '--time', type=int, default=30,
                        help='监听时长（秒），默认30秒')
    parser.add_argument('-m', '--min-packets', type=int, default=5,
                        help='最小数据包数量阈值，默认5个')
    
    args = parser.parse_args()
    
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    
    # 创建检测器
    detector = LidarNetworkDetector(
        interface=args.interface,
        duration=args.time,
        min_packets=args.min_packets
    )
    
    # 开始检测
    result = detector.detect_lidar_params()
    
    if result:
        print(f"\n✅ 检测成功！建议的激光雷达配置:")
        print(f"   目标IP: {result['target_ip']}")
        print(f"   数据端口: {result['data_port']}")
        print(f"   设备端口: {result['device_port']}")
    else:
        print(f"\n❌ 检测失败，请检查激光雷达连接状态")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
